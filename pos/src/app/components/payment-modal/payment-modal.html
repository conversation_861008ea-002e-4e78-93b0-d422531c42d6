<p-dialog [header]="title" [modal]="true" [(visible)]="visible" [style]="{width: '50vw', 'max-width':'800px'}"
  [breakpoints]="{'960px': '75vw', '640px': '95vw'}" [closable]="false" [draggable]="false" [resizable]="false">

  <div class="flex flex-col gap-4 p-4 text-md">
    <!-- Customer Information -->
    <div class="p-4 mb-4 border border-gray-200 rounded-lg bg-gray-50">
      <h6 class="mb-3 text-lg font-semibold text-gray-800">Customer Information</h6>
      <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
        <p-floatlabel variant="on" styleClass="w-full">
          <input pInputText id="customerName" [(ngModel)]="customerName" [disabled]="isProcessing" autocomplete="off" class="w-full" />
          <label for="customerName">Customer Name</label>
        </p-floatlabel>
        <p-floatlabel variant="on" styleClass="w-full">
          <input pInputText id="customerPhone" [(ngModel)]="customerPhone" [disabled]="isProcessing" autocomplete="off" class="w-full" />
          <label for="customerPhone">Phone Number</label>
        </p-floatlabel>
      </div>
    </div>
    <!-- Payment Methods -->
    <div>
      <h6 class="mb-3 text-lg font-semibold text-gray-800">Select Payment Method</h6>
      <div class="grid grid-cols-2 gap-3 mb-6 lg:grid-cols-4">
        <div *ngFor="let m of paymentMethods; trackBy: trackByMethod" 
             (click)="handlePaymentMethodChange(m.value)"
             class="p-3 transition-all duration-200 border-2 rounded-lg cursor-pointer hover:shadow-md"
             [class.border-blue-500]="paymentData.selectedPaymentMethod === m.value"
             [class.bg-blue-50]="paymentData.selectedPaymentMethod === m.value"
             [class.border-gray-200]="paymentData.selectedPaymentMethod !== m.value"
             [class.hover:border-blue-300]="paymentData.selectedPaymentMethod !== m.value"
             [class.opacity-60]="isProcessing">
          <div class="flex flex-col items-center justify-center min-h-[70px]">
            <i [class]="m.icon + ' text-2xl mb-2'" [ngClass]="paymentData.selectedPaymentMethod === m.value ? 'text-blue-600' : 'text-gray-600'"></i>
            <span class="text-sm font-medium text-center" [ngClass]="paymentData.selectedPaymentMethod === m.value ? 'text-blue-700' : 'text-gray-700'">
              {{m.label}}
            </span>
          </div>
        </div>
      </div>

      <!-- Payment Details -->
      <div class="mb-6">
        <ng-container *ngIf="getSelectedMethod() as method">
          <div class="p-4 border rounded-lg" [ngClass]="getClasses(method.color, 'detail')">
            <div class="flex items-center gap-3 mb-4">
              <i [class]="method.icon + ' ' + getClasses(method.color, 'icon')"></i>
              <h4 class="text-lg font-semibold" [ngClass]="getClasses(method.color, 'label')">{{method.label}} Payment</h4>
            </div>

            <div class="space-y-4">
              <ng-container *ngFor="let f of method.fields">
                <div *ngIf="f.type === 'amount'" class="mb-3">
                  <label class="block mb-1 text-sm font-medium text-gray-700">{{f.label}}</label>
                  <input pInputText #amountInput type="number" [value]="paymentData[f.key] || ''"
                    (input)="onAmountInput(amountInput, f.key)" [min]="f.min" [max]="f.max"
                    [disabled]="isProcessing" class="w-full p-2 border rounded" step="0.01" autocomplete="off">
                </div>

                <div *ngIf="f.type === 'text'" class="mb-3">
                  <label class="block mb-1 text-sm font-medium text-gray-700">{{f.label}}</label>
                  <input type="text" pInputText [placeholder]="f.placeholder || ''"
                    [(ngModel)]="paymentData[f.key]" [disabled]="isProcessing" class="w-full"
                    [ngClass]="{'p-invalid': f.required && !paymentData[f.key]}">
                </div>

                <div *ngIf="f.type === 'radio'" class="mb-3">
                  <label class="block mb-2 text-sm font-medium text-gray-700">{{f.label}}</label>
                  <div class="flex items-center gap-6">
                    <div *ngFor="let o of f.options" class="flex items-center gap-2">
                      <p-radioButton [name]="f.key" [value]="o.value" [(ngModel)]="paymentData[f.key]"
                        [disabled]="isProcessing" [inputId]="f.key + o.value"></p-radioButton>
                      <label [for]="f.key + o.value" class="cursor-pointer">{{o.label}}</label>
                    </div>
                  </div>
                </div>
              </ng-container>
            </div>
          </div>
        </ng-container>
      </div>
    </div>

    <!-- Order Summary -->
    <div class="p-4 border border-gray-200 rounded-lg bg-gray-50">
      <div class="grid grid-cols-1 gap-2 text-2xl">
        <div class="flex justify-between">
          <span class="font-bold text-gray-800">Total Amount :</span>
          <span class="font-bold">{{ totalAmount | currency:'INR' }}</span>
        </div>
        <div class="flex justify-between text-lg text-gray-600">
          <span>Remaining Amount :</span>
          <span>{{ remainingAmount | currency:'INR' }}</span>
        </div>
        <div *ngIf="change > 0" class="flex justify-between text-lg text-green-600">
          <span>Change :</span>
          <span>{{ change | currency:'INR' }}</span>
        </div>
      </div>
    </div>
  </div>

  <!-- Footer Buttons -->
  <ng-template pTemplate="footer">
    <div class="flex justify-end w-full gap-2">
      <button pButton pRipple type="button" [label]="cancelButtonLabel" class="p-button-text"
        [disabled]="isProcessing" (click)="onCancel()"></button>
      <button pButton pRipple type="button" [label]="confirmButtonLabel"
        [disabled]="isProcessing || remainingAmount > 0" [loading]="isProcessing" (click)="onConfirm()"></button>
    </div>
  </ng-template>
</p-dialog>