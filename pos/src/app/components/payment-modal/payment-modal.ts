import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output, OnChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { InputNumberModule } from 'primeng/inputnumber';
import { DialogModule } from 'primeng/dialog';
import { RadioButtonModule } from 'primeng/radiobutton';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { FloatLabelModule } from 'primeng/floatlabel';

import { PaymentConfigService, PaymentMethod } from '../../services/payment/payment.config.service';
import { PaymentValidationService } from '../../services/payment/payment.validation.service';

interface PaymentModalOutput {
  paymentMethod: string;
  paymentData: any;
  customerName: string;
  customerPhone: string;
}

@Component({
  selector: 'app-payment-modal',
  standalone: true,
  templateUrl: './payment-modal.html',
  imports: [
    CommonModule, FormsModule, InputNumberModule, DialogModule,
    RadioButtonModule, ButtonModule, InputTextModule, FloatLabelModule
  ],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class PaymentModalComponent implements OnChanges {
  @Input() visible = false;
  @Input() totalAmount = 0;
  @Input() isProcessing = false;
  @Input() title = 'Complete Payment';
  @Input() confirmButtonLabel = 'Confirm Payment';
  @Input() cancelButtonLabel = 'Cancel';
  
  @Output() visibleChange = new EventEmitter<boolean>();
  @Output() cancel = new EventEmitter<void>();
  @Output() confirm = new EventEmitter<PaymentModalOutput>();

  paymentData: any = { selectedPaymentMethod: 'cash' };
  remainingAmount = 0;
  change = 0;
  customerName = '';
  customerPhone = '';
  paymentMethods: PaymentMethod[] = [];

  constructor(
    private paymentConfig: PaymentConfigService,
    private paymentValidation: PaymentValidationService
  ) {}

  ngOnChanges(): void {
    if (this.totalAmount !== undefined) {
      this.updateRemainingAmount();
      this.paymentMethods = this.paymentConfig.getPaymentMethods(this.totalAmount);
    }
    if (this.visible) this.resetPaymentData();
  }

  trackByMethod(_: number, m: PaymentMethod): string { return m.value; }
  getSelectedMethod = () => this.paymentMethods.find(m => m.value === this.paymentData.selectedPaymentMethod);

  private resetPaymentData(): void {
    this.paymentData = this.paymentConfig.initializePaymentData(this.totalAmount);
    this.updateRemainingAmount(); // Ensure remaining amount is calculated on reset
  }

  updateRemainingAmount(): void {
    const amountKey = `${this.paymentData.selectedPaymentMethod}Amount`;
    const paidAmount = parseFloat(this.paymentData[amountKey] || 0);
    this.remainingAmount = Math.max(0, this.totalAmount - paidAmount);
    this.change = Math.max(0, paidAmount - this.totalAmount);
  }

  handlePaymentMethodChange(method: string): void {
    if (!this.isProcessing) {
      this.paymentData.selectedPaymentMethod = method;
      this.updateRemainingAmount();
    }
  }

  onAmountInput(input: HTMLInputElement, fieldKey: string): void {
    this.paymentData[fieldKey] = parseFloat(input.value) || 0;
    this.updateRemainingAmount();
  }

  onConfirm(): void {
    if (this.isProcessing) return;
    const { paymentData, customerName, customerPhone } = this;
    if (this.paymentValidation.validateForm(paymentData, customerName, customerPhone, this.totalAmount)) {
      this.confirm.emit({
        paymentMethod: paymentData.selectedPaymentMethod,
        paymentData,
        customerName,
        customerPhone
      });
    }
  }

  onCancel(): void {
    if (!this.isProcessing) {
      this.visible = false;
      this.visibleChange.emit(false);
      this.cancel.emit();
    }
  }

  // Template helpers
  getClasses = (color: string, type: 'detail' | 'icon' | 'label') => 
    type === 'detail' ? `border-${color}-200 bg-${color}-50` :
    type === 'icon' ? `text-${color}-600 text-2xl` :
    `text-${color}-800`;
}