import { Injectable } from '@angular/core';

export interface PaymentMethodField {
  key: string;
  label: string;
  type: 'amount' | 'text' | 'radio' | 'select';
  required?: boolean;
  min?: number;
  max?: number;
  placeholder?: string;
  options?: { label: string; value: any }[];
  showButtons?: boolean;
}

export interface PaymentMethod {
  value: string;
  label: string;
  icon: string;
  color: string;
  fields: PaymentMethodField[];
  addUserButton?: boolean;
}

export interface PaymentData {
  selectedPaymentMethod: string;
  [key: string]: any;
}

@Injectable({ providedIn: 'root' })
export class PaymentConfigService {
  private paymentMethods: PaymentMethod[] = [];
  private  MAX_AMOUNT = this.totalAmount;
  private methodsCache = new Map<number, PaymentMethod[]>();
  totalAmount: any;

  constructor() { this.initializePaymentMethods(); }

  getPaymentMethods(totalAmount: number): PaymentMethod[] {
    if (!this.methodsCache.has(totalAmount)) {
      this.methodsCache.set(totalAmount, this.paymentMethods.map(m => ({
        ...m,
        fields: m.fields.map(f => ({
          ...f,
          max: f.type === 'amount' ? Math.max(totalAmount * 2, this.MAX_AMOUNT) : f.max
        }))
      })));
    }
    return this.methodsCache.get(totalAmount)!;
  }

  getPaymentMethod(method: string, totalAmount: number): PaymentMethod | undefined {
    return this.getPaymentMethods(totalAmount).find(m => m.value === method);
  }

  initializePaymentData(totalAmount: number): PaymentData {
    const data: PaymentData = { selectedPaymentMethod: 'cash' };
    const cashMethod = this.paymentMethods.find(m => m.value === 'cash');
    if (cashMethod) data[`${cashMethod.value}Amount`] = totalAmount;
    return data;
  }

  private initializePaymentMethods(): void {
    this.paymentMethods = [
      this.createMethod('cash', 'Cash', 'pi pi-money-bill', 'green', [
        { key: 'cashAmount', label: 'Amount Received', type: 'amount', required: true, min: 0, showButtons: true }
      ]),
      this.createMethod('upi', 'UPI', 'pi pi-wallet', 'blue', [
        { key: 'upiAmount', label: 'Amount', type: 'amount', required: true, min: 1, showButtons: true },
        { key: 'upiId', label: 'UPI ID', type: 'text', placeholder: 'example@upi', required: true }
      ]),
      this.createMethod('card', 'Card', 'pi pi-credit-card', 'indigo', [
        { key: 'cardAmount', label: 'Amount', type: 'amount', required: true, min: 1, showButtons: true },
        { 
          key: 'cardType', 
          label: 'Card Type', 
          type: 'radio', 
          required: true,
          options: [{ label: 'Credit', value: 'credit' }, { label: 'Debit', value: 'debit' }]
        }
      ]),
      this.createMethod('partial', 'Partial', 'pi pi-wallet', 'orange', [
        { key: 'partialAmount', label: 'Amount', type: 'amount', required: true, min: 1, showButtons: true }
      ])
    ];
  }

  private createMethod(
    value: string, 
    label: string, 
    icon: string, 
    color: string, 
    fields: Partial<PaymentMethodField>[]
  ): PaymentMethod {
    return {
      value, label, icon, color,
      fields: fields.map(f => ({
        ...f,
        required: f.required ?? true,
        type: f.type || 'text',
        min: f.min ?? 0,
        max: f.max ?? this.MAX_AMOUNT
      })) as PaymentMethodField[]
    };
  }
}
