import { Injectable } from '@angular/core';
import { MessageService } from 'primeng/api';
import { PaymentConfigService } from './payment.config.service';

@Injectable({ providedIn: 'root' })
export class PaymentValidationService {
  constructor(
    private messageService: MessageService,
    private paymentConfig: PaymentConfigService
  ) {}

  validateForm(paymentData: any, customerName: string, customerPhone: string, total: number): boolean {
    if (!customerName?.trim()) return this.showError('Customer name is required');
    if (!customerPhone?.trim()) return this.showError('Customer phone is required');

    const method = paymentData.selectedPaymentMethod;
    const methodData = this.paymentConfig.getPaymentMethod(method, total);
    if (!methodData) return this.showError('Invalid payment method');

    return this.validateRequiredFields(paymentData, methodData) && 
           this.validatePaymentAmount(paymentData, method, total);
  }

  private validateRequiredFields(paymentData: any, methodData: any): boolean {
    for (const field of methodData.fields) {
      if (field.required && !paymentData[field.key]?.toString().trim()) {
        return this.showError(`${field.label} is required`);
      }
    }
    return true;
  }

  private validatePaymentAmount(paymentData: any, method: string, total: number): boolean {
    const amount = parseFloat(paymentData[`${method}Amount`] || 0);
    
    if (amount <= 0) return this.showError('Amount must be greater than 0');
    if (amount < total) return this.showError(`Amount must be at least ${total}`);
    if (amount > total * 2) return this.showError('Amount exceeds reasonable limit');
    
    return true;
  }

  private showError(detail: string): boolean {
    this.messageService.add({ severity: 'error', summary: 'Error', detail });
    return false;
  }
}